<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Grid - Pinned Columns Test</title>
    <link rel="stylesheet" href="snap-grid.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #f0f0f0;
        }
        
        .btn.primary {
            background: #04AE2C;
            color: white;
            border-color: #04AE2C;
        }
        
        .btn.primary:hover {
            background: #038a24;
        }
        
        .grid-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Snap Grid - Enhanced Pinned Columns Test</h1>
        <p>Test the new AG Grid-style pinned columns functionality with separate containers and enhanced API.</p>
        
        <div class="controls">
            <button class="btn primary" onclick="pinColumn('name', 'left')">Pin Name Left</button>
            <button class="btn primary" onclick="pinColumn('email', 'left')">Pin Email Left</button>
            <button class="btn primary" onclick="pinColumn('actions', 'right')">Pin Actions Right</button>
            <button class="btn primary" onclick="pinColumn('status', 'right')">Pin Status Right</button>
            
            <button class="btn" onclick="unpinColumn('name')">Unpin Name</button>
            <button class="btn" onclick="unpinColumn('email')">Unpin Email</button>
            <button class="btn" onclick="unpinColumn('actions')">Unpin Actions</button>
            <button class="btn" onclick="unpinColumn('status')">Unpin Status</button>
            
            <button class="btn" onclick="clearAllPinned()">Clear All Pinned</button>
            <button class="btn" onclick="refreshGrid()">Refresh Grid</button>
        </div>
        
        <div id="grid" class="grid-container"></div>
        
        <div class="info">
            <h3>API Methods Available:</h3>
            <ul>
                <li><code>grid.pinColumn(columnId, position)</code> - Pin a column left or right</li>
                <li><code>grid.unpinColumn(columnId)</code> - Unpin a column</li>
                <li><code>grid.isColumnPinned(columnId)</code> - Check if column is pinned</li>
                <li><code>grid.getPinnedColumns(position)</code> - Get pinned columns</li>
                <li><code>grid.setPinnedColumns(columnIds, position)</code> - Set multiple pinned columns</li>
                <li><code>grid.getColumnState()</code> - Get full column state</li>
                <li><code>grid.setColumnState(columnStates)</code> - Set column state</li>
            </ul>
            
            <h3>Current State:</h3>
            <div id="state-info"></div>
        </div>
    </div>

    <script src="snap-grid.js"></script>
    <script>
        // Sample data
        const sampleData = [
            { id: 1, name: 'John Doe', email: '<EMAIL>', age: 30, department: 'Engineering', status: 'Active', salary: 75000 },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', age: 28, department: 'Marketing', status: 'Active', salary: 68000 },
            { id: 3, name: 'Bob Johnson', email: '<EMAIL>', age: 35, department: 'Sales', status: 'Inactive', salary: 72000 },
            { id: 4, name: 'Alice Brown', email: '<EMAIL>', age: 32, department: 'Engineering', status: 'Active', salary: 80000 },
            { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', age: 29, department: 'HR', status: 'Active', salary: 65000 },
            { id: 6, name: 'Diana Lee', email: '<EMAIL>', age: 31, department: 'Marketing', status: 'Inactive', salary: 70000 },
            { id: 7, name: 'Eve Davis', email: '<EMAIL>', age: 27, department: 'Sales', status: 'Active', salary: 69000 },
            { id: 8, name: 'Frank Miller', email: '<EMAIL>', age: 33, department: 'Engineering', status: 'Active', salary: 85000 }
        ];

        // Column definitions
        const columns = [
            { field: 'checkbox', headerName: '', width: 50, pinned: 'left' },
            { field: 'name', headerName: 'Name', width: 150, sortable: true },
            { field: 'email', headerName: 'Email', width: 200, sortable: true },
            { field: 'age', headerName: 'Age', width: 80, type: 'number', sortable: true },
            { field: 'department', headerName: 'Department', width: 120, sortable: true },
            { field: 'salary', headerName: 'Salary', width: 100, type: 'currency', sortable: true },
            { field: 'status', headerName: 'Status', width: 100, sortable: true },
            { field: 'actions', headerName: 'Actions', width: 100, pinned: 'right' }
        ];

        // Initialize grid
        let grid;
        
        function initGrid() {
            grid = new SnapGrid('grid', {
                data: sampleData,
                columns: columns,
                virtualScrolling: true,
                sortable: true,
                filterable: true,
                resizable: true,
                selectable: true,
                checkboxSelection: true,
                columnDragging: true,
                onColumnPinned: (columnId, position) => {
                    console.log(`Column ${columnId} pinned to ${position}`);
                    updateStateInfo();
                },
                onColumnUnpinned: (columnId) => {
                    console.log(`Column ${columnId} unpinned`);
                    updateStateInfo();
                }
            });
            
            updateStateInfo();
        }

        // API test functions
        function pinColumn(columnId, position) {
            try {
                grid.pinColumn(columnId, position);
                console.log(`Pinned ${columnId} to ${position}`);
            } catch (error) {
                console.error('Error pinning column:', error);
            }
        }

        function unpinColumn(columnId) {
            try {
                grid.unpinColumn(columnId);
                console.log(`Unpinned ${columnId}`);
            } catch (error) {
                console.error('Error unpinning column:', error);
            }
        }

        function clearAllPinned() {
            try {
                const pinnedLeft = grid.getPinnedColumns('left');
                const pinnedRight = grid.getPinnedColumns('right');
                
                pinnedLeft.forEach(col => grid.unpinColumn(col.field));
                pinnedRight.forEach(col => grid.unpinColumn(col.field));
                
                console.log('Cleared all pinned columns');
            } catch (error) {
                console.error('Error clearing pinned columns:', error);
            }
        }

        function refreshGrid() {
            grid.refresh();
            updateStateInfo();
        }

        function updateStateInfo() {
            const stateInfo = document.getElementById('state-info');
            const columnState = grid.getColumnState();
            const pinnedLeft = grid.getPinnedColumns('left');
            const pinnedRight = grid.getPinnedColumns('right');
            
            stateInfo.innerHTML = `
                <p><strong>Pinned Left:</strong> ${pinnedLeft.map(col => col.field).join(', ') || 'None'}</p>
                <p><strong>Pinned Right:</strong> ${pinnedRight.map(col => col.field).join(', ') || 'None'}</p>
                <p><strong>Total Columns:</strong> ${columnState.length}</p>
                <p><strong>Column State:</strong> <pre>${JSON.stringify(columnState, null, 2)}</pre></p>
            `;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initGrid);
    </script>
</body>
</html>
