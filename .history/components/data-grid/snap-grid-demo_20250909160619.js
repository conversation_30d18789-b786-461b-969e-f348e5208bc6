/**
 * SnapGrid Demo JavaScript
 * Demonstrates all features and capabilities of the SnapGrid component
 */

// Global variables
let grid = null;
let currentData = [];

// Sample data generators
const sampleNames = [
    'iPhone 14 Pro', 'Samsung Galaxy S23', 'MacBook Pro M2', 'Dell XPS 13', 'iPad Air',
    'Surface Pro 9', 'AirPods Pro', 'Sony WH-1000XM4', 'Nintendo Switch', 'PlayStation 5',
    'Xbox Series X', 'Apple Watch Series 8', 'Fitbit Versa 4', 'Canon EOS R6', 'Sony A7 IV',
    'GoPro Hero 11', 'DJI Mini 3', 'Tesla Model Y', 'BMW i4', 'Mercedes EQS',
    'Audi e-tron GT', 'Porsche Taycan', 'Lucid Air', '<PERSON>ivian R1T', 'Ford F-150 Lightning'
];

const sampleCategories = [
    'Electronics', 'Smartphones', 'Laptops', 'Tablets', 'Audio', 'Gaming', 'Wearables',
    'Cameras', 'Drones', 'Automotive', 'Electric Vehicles', 'Accessories'
];

const sampleBrands = [
    'Apple', 'Samsung', 'Dell', 'Microsoft', 'Sony', 'Nintendo', 'Canon', 'DJI',
    'Tesla', 'BMW', 'Mercedes', 'Audi', 'Porsche', 'Lucid', 'Rivian', 'Ford'
];

// Generate sample data
function generateSampleData(count = 1000) {
    const data = [];
    
    for (let i = 0; i < count; i++) {
        const name = sampleNames[Math.floor(Math.random() * sampleNames.length)];
        const category = sampleCategories[Math.floor(Math.random() * sampleCategories.length)];
        const brand = sampleBrands[Math.floor(Math.random() * sampleBrands.length)];
        
        data.push({
            id: i + 1,
            name: `${name} ${i + 1}`,
            category: category,
            brand: brand,
            price: Math.floor(Math.random() * 5000) + 100,
            stock: Math.floor(Math.random() * 1000),
            rating: (Math.random() * 4 + 1).toFixed(1),
            inStock: Math.random() > 0.2,
            releaseDate: new Date(2020 + Math.floor(Math.random() * 4), 
                                Math.floor(Math.random() * 12), 
                                Math.floor(Math.random() * 28) + 1),
            description: `High-quality ${category.toLowerCase()} from ${brand}`,
            sales: Math.floor(Math.random() * 10000),
            revenue: 0 // Will be calculated
        });
        
        // Calculate revenue
        data[i].revenue = data[i].price * data[i].sales;
    }
    
    return data;
}

// Column definitions
const columnDefs = [
    {
        field: 'id',
        headerName: 'ID',
        width: 80,
        type: 'number',
        sortable: true,
        filterable: true
    },
    {
        field: 'name',
        headerName: 'Product Name',
        width: 250,
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'category',
        headerName: 'Category',
        width: 150,
        sortable: true,
        filterable: true
    },
    {
        field: 'brand',
        headerName: 'Brand',
        width: 120,
        sortable: true,
        filterable: true
    },
    {
        field: 'price',
        headerName: 'Price',
        width: 120,
        type: 'currency',
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'stock',
        headerName: 'Stock',
        width: 100,
        type: 'number',
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'rating',
        headerName: 'Rating',
        width: 100,
        type: 'number',
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            const stars = '★'.repeat(Math.floor(value)) + '☆'.repeat(5 - Math.floor(value));
            return `<span title="${value}/5">${stars}</span>`;
        }
    },
    {
        field: 'inStock',
        headerName: 'In Stock',
        width: 100,
        type: 'boolean',
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            const color = value ? '#4CAF50' : '#F44336';
            const text = value ? 'Yes' : 'No';
            return `<span style="color: ${color}; font-weight: 500;">${text}</span>`;
        }
    },
    {
        field: 'releaseDate',
        headerName: 'Release Date',
        width: 140,
        type: 'date',
        sortable: true,
        filterable: true
    },
    {
        field: 'sales',
        headerName: 'Sales',
        width: 120,
        type: 'number',
        sortable: true,
        filterable: true
    },
    {
        field: 'revenue',
        headerName: 'Revenue',
        width: 140,
        type: 'currency',
        sortable: true,
        filterable: true
    }
];

// Initialize the demo
function initDemo() {
    console.log('🚀 Initializing SnapGrid Demo...');
    
    // Generate initial data
    currentData = generateSampleData(1000);
    
    // Create grid
    createGrid();
    
    // Set up event listeners
    setupEventListeners();
    
    // Update stats
    updateStats();
    
    console.log('✅ SnapGrid Demo initialized successfully');
}

// Create the grid instance
function createGrid() {
    const container = document.getElementById('demoGrid');
    
    if (grid) {
        grid.destroy();
    }
    
    grid = new SnapGrid(container, {
        data: currentData,
        columns: columnDefs,
        virtualScrolling: true,
        sortable: true,
        filterable: true,
        resizable: true,
        selectable: true,
        editable: true,
        theme: 'default',
        
        // Callbacks
        onRowClick: (rowData, rowIndex, event) => {
            console.log('Row clicked:', rowData);
            updateStats();
        },
        
        onCellEdit: (field, newValue, oldValue, rowData, rowIndex) => {
            console.log('Cell edited:', { field, newValue, oldValue, rowData });
            
            // Recalculate revenue if price or sales changed
            if (field === 'price' || field === 'sales') {
                rowData.revenue = rowData.price * rowData.sales;
                grid.refresh();
            }
            
            updateStats();
        },
        
        onSort: (field, direction) => {
            console.log('Sort applied:', { field, direction });
            updateStats();
        },
        
        onFilter: (field, value, type) => {
            console.log('Filter applied:', { field, value, type });
            updateStats();
        }
    });
}

// Set up event listeners
function setupEventListeners() {
    // Theme selector
    document.getElementById('themeSelect').addEventListener('change', (e) => {
        const theme = e.target.value;
        const container = document.getElementById('demoGrid');
        container.className = `snap-grid ${theme}`;
        grid.options.theme = theme;
    });
    
    // Row count selector
    document.getElementById('rowCountSelect').addEventListener('change', (e) => {
        const count = parseInt(e.target.value);
        console.log(`🔄 Generating ${count} rows...`);
        
        const startTime = performance.now();
        currentData = generateSampleData(count);
        grid.updateData(currentData);
        const endTime = performance.now();
        
        console.log(`✅ Generated ${count} rows in ${(endTime - startTime).toFixed(2)}ms`);
        updateStats();
    });
    
    // Filter input
    document.getElementById('filterInput').addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
            applyFilter();
        }
    });
}

// Apply filter
function applyFilter() {
    const filterValue = document.getElementById('filterInput').value;
    if (filterValue.trim()) {
        grid.setFilter('name', filterValue, 'contains');
    } else {
        clearFilter();
    }
    updateStats();
}

// Clear filter
function clearFilter() {
    document.getElementById('filterInput').value = '';
    grid.clearFilters();
    updateStats();
}

// Export data
function exportData() {
    const filename = `snap-grid-export-${new Date().toISOString().split('T')[0]}.csv`;
    grid.exportToCsv(filename);
    console.log('📊 Data exported to:', filename);
}

// Refresh grid
function refreshGrid() {
    grid.refresh();
    updateStats();
    console.log('🔄 Grid refreshed');
}

// Clear selection
function clearSelection() {
    grid.clearSelection();
    updateStats();
    console.log('❌ Selection cleared');
}

// Update statistics
function updateStats() {
    if (!grid) return;
    
    const stats = grid.getStats();
    
    document.getElementById('totalRows').textContent = stats.totalRows.toLocaleString();
    document.getElementById('filteredRows').textContent = stats.filteredRows.toLocaleString();
    document.getElementById('selectedRows').textContent = stats.selectedRows.toLocaleString();
    document.getElementById('renderTime').textContent = `${stats.lastRenderDuration.toFixed(1)}ms`;
}

// Toggle theme
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    // Update button text
    const button = document.querySelector('.theme-toggle');
    button.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
}

// Initialize theme from localStorage
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    const button = document.querySelector('.theme-toggle');
    button.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    initTheme();
    initDemo();
});

// Performance monitoring
setInterval(() => {
    if (grid) {
        updateStats();
    }
}, 1000);
