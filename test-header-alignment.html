<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Grid Header Alignment Test</title>
    <link rel="stylesheet" href="tokens.css">
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-grid {
            height: 600px;
            margin: 20px 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        
        .test-controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 8px 16px;
            background: var(--btn-primary);
            color: var(--btn-text);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: var(--btn-hover);
        }
        
        .test-info {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: var(--text-accent);
        }
        
        .test-info p {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Snap Grid Header-Column Alignment Test</h1>
        
        <div class="test-info">
            <h3>Test Instructions:</h3>
            <p>1. <strong>Resize columns</strong> by dragging the column borders - headers should stay aligned with data</p>
            <p>2. <strong>Scroll horizontally</strong> - headers should move in perfect sync with data columns</p>
            <p>3. <strong>Rearrange columns</strong> by dragging column headers - alignment should be maintained</p>
            <p>4. <strong>Use test buttons</strong> below to programmatically test various scenarios</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="testColumnResize()">Test Column Resize</button>
            <button class="test-btn" onclick="testColumnMove()">Test Column Move</button>
            <button class="test-btn" onclick="testScrollSync()">Test Scroll Sync</button>
            <button class="test-btn" onclick="testWidthSync()">Test Width Sync</button>
            <button class="test-btn" onclick="resetGrid()">Reset Grid</button>
        </div>
        
        <div id="test-grid" class="test-grid"></div>
        
        <div class="test-info">
            <h3>Expected Behavior:</h3>
            <p>✅ Headers and data columns should always be perfectly aligned</p>
            <p>✅ Horizontal scrolling should keep header and body in sync</p>
            <p>✅ Column resizing should update both header and data cells simultaneously</p>
            <p>✅ Column rearranging should maintain alignment</p>
            <p>❌ No misalignment between headers and their corresponding data columns</p>
        </div>
    </div>

    <script src="components/data-grid/snap-grid.js"></script>
    <script>
        let grid;
        
        // Sample data for testing
        const testData = Array.from({ length: 100 }, (_, i) => ({
            id: i + 1,
            name: `Product ${i + 1}`,
            category: ['Electronics', 'Clothing', 'Books', 'Home'][i % 4],
            price: (Math.random() * 100 + 10).toFixed(2),
            stock: Math.floor(Math.random() * 100),
            description: `This is a detailed description for product ${i + 1} with some longer text to test column width handling.`,
            date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
            status: ['Active', 'Inactive', 'Pending'][i % 3],
            rating: (Math.random() * 5).toFixed(1)
        }));
        
        const testColumns = [
            { field: 'id', headerName: 'ID', width: 80, sortable: true },
            { field: 'name', headerName: 'Product Name', width: 200, sortable: true, filterable: true },
            { field: 'category', headerName: 'Category', width: 120, sortable: true, filterable: true },
            { field: 'price', headerName: 'Price ($)', width: 100, sortable: true },
            { field: 'stock', headerName: 'Stock', width: 80, sortable: true },
            { field: 'description', headerName: 'Description', width: 300, sortable: false },
            { field: 'date', headerName: 'Date', width: 120, sortable: true },
            { field: 'status', headerName: 'Status', width: 100, sortable: true, filterable: true },
            { field: 'rating', headerName: 'Rating', width: 80, sortable: true }
        ];
        
        function initGrid() {
            const container = document.getElementById('test-grid');
            grid = new SnapGrid(container, {
                data: testData,
                columns: testColumns,
                virtualScrolling: true,
                resizable: true,
                sortable: true,
                filterable: true,
                columnDragging: true,
                checkboxSelection: true
            });
        }
        
        function testColumnResize() {
            console.log('Testing column resize...');
            // Programmatically resize a column
            grid.columnApi.setColumnWidth('name', 250);
            grid.columnApi.setColumnWidth('description', 400);
            console.log('Column resize test completed');
        }
        
        function testColumnMove() {
            console.log('Testing column move...');
            // Move the price column to a different position
            grid.columnApi.moveColumnByField('price', 2);
            console.log('Column move test completed');
        }
        
        function testScrollSync() {
            console.log('Testing scroll sync...');
            // Programmatically scroll to test sync
            if (grid.bodyElement) {
                grid.bodyElement.scrollLeft = 200;
            }
            console.log('Scroll sync test completed');
        }
        
        function testWidthSync() {
            console.log('Testing width synchronization...');
            // Force a width recalculation
            grid.updateHorizontalMetrics();
            grid.syncHorizontalScroll();
            console.log('Width sync test completed');
        }
        
        function resetGrid() {
            console.log('Resetting grid...');
            const container = document.getElementById('test-grid');
            container.innerHTML = '';
            initGrid();
            console.log('Grid reset completed');
        }
        
        // Initialize the grid when page loads
        document.addEventListener('DOMContentLoaded', initGrid);
    </script>
</body>
</html>
