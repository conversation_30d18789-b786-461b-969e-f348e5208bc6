<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Updated Test - Checkbox & Column Dragging</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="../../css/tokens.css">
    <link rel="stylesheet" href="../../css/base.css">
    
    <!-- SnapGrid CSS -->
    <link rel="stylesheet" href="snap-grid.css">
    
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-header {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-accent);
            margin-bottom: 10px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .test-button {
            padding: 8px 16px;
            border: 1px solid var(--btn-border);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: var(--btn-hover);
        }
        
        .test-button.primary {
            background: var(--palette-blue-primary);
            color: white;
            border-color: var(--palette-blue-primary);
        }
        
        .test-button.active {
            background: var(--palette-blue-primary);
            color: white;
            border-color: var(--palette-blue-primary);
        }
        
        .dataset-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: auto;
        }
        
        .dataset-label {
            font-weight: 600;
            margin-right: 10px;
        }
        
        .grid-container {
            height: 500px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-results {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .test-log {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            background: var(--bg-primary);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            white-space: pre-wrap;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px;
            border: 1px solid var(--btn-border);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }
        
        .stats-display {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .stat-item {
            background: var(--bg-primary);
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }
        
        .stat-label {
            font-weight: 600;
            color: var(--text-accent);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Theme</button>
    
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">SnapGrid Updated Test</h1>
            <p>Testing checkbox selection, column dragging, and new data structure</p>
        </div>
        
        <div class="test-controls">
            <button class="test-button primary" onclick="initializeGrid()">Initialize Grid</button>
            <button class="test-button" onclick="testCheckboxes()">Test Checkboxes</button>
            <button class="test-button" onclick="testColumnDragging()">Test Column Dragging</button>
            <button class="test-button" onclick="exportSelected()">Export Selected</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            
            <div class="dataset-controls">
                <span class="dataset-label">Dataset:</span>
                <button class="test-button" onclick="loadDataset(500)" id="btn-500">500 rows</button>
                <button class="test-button active" onclick="loadDataset(1000)" id="btn-1000">1K rows</button>
                <button class="test-button" onclick="loadDataset(10000)" id="btn-10000">10K rows</button>
            </div>
        </div>
        
        <div class="stats-display">
            <div class="stat-item">
                <span class="stat-label">Total Rows:</span> <span id="totalRows">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Selected:</span> <span id="selectedRows">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Render Time:</span> <span id="renderTime">0ms</span>
            </div>
        </div>
        
        <div class="grid-container">
            <div id="testGrid"></div>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="testLog" class="test-log">Ready to run tests...\n</div>
        </div>
    </div>
    
    <!-- Old Snap Grid dataset generator -->
    <script src="../../old Snap data-grid Ref code/dummy-grid-data.js"></script>
    <!-- SnapGrid JavaScript -->
    <script src="snap-grid.js"></script>
    
    <script>
        let grid = null;
        let currentDataset = 1000;
        
        // Use old Snap Grid dataset generator to match exact fields/types
        function generateSampleData(count = 1000) {
            return generateProductData(count, { seed: 1234 });
        }
        
        // Column definitions (old grid order and types)
        const columnDefs = [
            { field: 'marketplace', headerName: 'Marketplace', width: 100, sortable: true, filterable: true,
              cellRenderer: (value) => `<img src="assets/${value}.svg" alt="${value}" style=\"width:16px;height:12px;margin-right:6px;vertical-align:middle;\" onerror=\"this.style.display='none'\"><span>${value}</span>` },
            { field: 'asin', headerName: 'ASIN', width: 120, sortable: true, filterable: true },
            { field: 'status', headerName: 'Status', width: 140, type: 'status', sortable: true, filterable: true },
            { field: 'productType', headerName: 'Product Type', width: 150, sortable: true, filterable: true },
            { field: 'brand', headerName: 'Brand', width: 140, sortable: true, filterable: true },
            { field: 'title', headerName: 'Product Title', width: 260, sortable: true, filterable: true },
            { field: 'price', headerName: 'Price', width: 100, type: 'currency', sortable: true, filterable: true },
            { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true, filterable: true },
            { field: 'returns', headerName: 'Returns', width: 100, type: 'number', sortable: true, filterable: true },
            { field: 'returnRate', headerName: 'Return Rate', width: 110, sortable: true, filterable: true,
              cellRenderer: (value) => value == null ? '' : `${value}%` },
            { field: 'royalties', headerName: 'Royalties', width: 120, type: 'currency', sortable: true, filterable: true },
            { field: 'firstSold', headerName: 'First Sold', width: 140, type: 'date', sortable: true, filterable: true },
            { field: 'lastSold', headerName: 'Last Sold', width: 140, type: 'date', sortable: true, filterable: true },
            { field: 'bsr', headerName: 'BSR', width: 120, type: 'number', sortable: true, filterable: true },
            { field: 'firstPublished', headerName: 'First Published', width: 160, type: 'date', sortable: true, filterable: true },
            { field: 'lastUpdated', headerName: 'Last Updated', width: 160, type: 'date', sortable: true, filterable: true },
            { field: 'reviews', headerName: 'Reviews', width: 110, type: 'number', sortable: true, filterable: true },
            { field: 'designId', headerName: 'Design ID', width: 140, sortable: true, filterable: true }
        ];
        
        // Logging function
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('testLog').textContent = 'Log cleared...\n';
        }
        
        // Update stats display
        function updateStats() {
            if (grid) {
                const stats = grid.getStats();
                const selectedData = grid.getSelectedData();
                
                document.getElementById('totalRows').textContent = stats.totalRows;
                document.getElementById('selectedRows').textContent = selectedData.length;
                document.getElementById('renderTime').textContent = stats.lastRenderDuration.toFixed(2) + 'ms';
            }
        }
        
        // Initialize grid
        function initializeGrid() {
            loadDataset(currentDataset);
        }
        
        // Load dataset
        function loadDataset(count) {
            log(`Loading ${count} rows...`);
            currentDataset = count;
            
            // Update button states
            document.querySelectorAll('.dataset-controls .test-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${count}`).classList.add('active');
            
            try {
                const data = generateSampleData(count);
                const container = document.getElementById('testGrid');
                
                if (grid) {
                    grid.destroy();
                    log('Destroyed existing grid');
                }
                
                const startTime = performance.now();
                
                grid = new SnapGrid(container, {
                    data: data,
                    columns: columnDefs,
                    checkboxSelection: true,
                    headerCheckboxSelection: true,
                    columnDragging: true,
                    virtualScrolling: true,
                    sortable: true,
                    filterable: true,
                    resizable: true,
                    editable: false,
                    
                    onSelectionChanged: (selectedData) => {
                        log(`Selection changed: ${selectedData.length} rows selected`);
                        updateStats();
                    },
                    
                    onColumnMoved: (fromIndex, toIndex) => {
                        log(`Column moved from index ${fromIndex} to ${toIndex}`);
                    },
                    
                    onRowClick: (rowData, rowIndex) => {
                        log(`Row clicked: ${rowData.productTitle} (index: ${rowIndex})`);
                    },
                    
                    onCellEdit: (field, newValue, oldValue, rowData) => {
                        log(`Cell edited: ${field} changed from "${oldValue}" to "${newValue}"`);
                    }
                });
                
                const endTime = performance.now();
                log(`Grid loaded with ${count} rows in ${(endTime - startTime).toFixed(2)}ms`);
                updateStats();
                
            } catch (error) {
                log(`Failed to load dataset: ${error.message}`);
                console.error('Dataset loading error:', error);
            }
        }
        
        // Test checkboxes
        function testCheckboxes() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            log('Testing checkbox functionality...');
            log('- Click individual row checkboxes to select/deselect');
            log('- Click header checkbox to select/deselect all');
            log('- Selection state will be logged automatically');
        }
        
        // Test column dragging
        function testColumnDragging() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            log('Testing column dragging...');
            log('- Hover over column headers (except checkbox column)');
            log('- Drag and drop to reorder columns');
            log('- Column moves will be logged automatically');
        }
        
        // Export selected rows
        function exportSelected() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            const selectedData = grid.getSelectedData();
            if (selectedData.length === 0) {
                log('No rows selected for export');
                return;
            }
            
            log(`Exporting ${selectedData.length} selected rows...`);
            
            // Create CSV content
            const headers = columnDefs.map(col => col.headerName || col.field);
            const csvContent = [
                headers.join(','),
                ...selectedData.map(row => 
                    columnDefs.map(col => {
                        const value = row[col.field];
                        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                    }).join(',')
                )
            ].join('\n');
            
            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `snap-grid-selected-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
            
            log(`Export completed: ${selectedData.length} rows`);
        }
        
        // Toggle theme
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
            
            log(`Theme changed to: ${newTheme}`);
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
            
            log('SnapGrid updated test page loaded. Click "Initialize Grid" to start.');
        });
    </script>
</body>
</html>
