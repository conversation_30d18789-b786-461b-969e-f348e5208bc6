/**
 * SnapGrid Demo JavaScript
 * Demonstrates all features and capabilities of the SnapGrid component
 */

// Global variables
let grid = null;
let currentData = [];

// Sample data generators
const sampleNames = [
    'iPhone 14 Pro', 'Samsung Galaxy S23', 'MacBook Pro M2', 'Dell XPS 13', 'iPad Air',
    'Surface Pro 9', 'AirPods Pro', 'Sony WH-1000XM4', 'Nintendo Switch', 'PlayStation 5',
    'Xbox Series X', 'Apple Watch Series 8', 'Fitbit Versa 4', 'Canon EOS R6', 'Sony A7 IV',
    'GoPro Hero 11', 'DJI Mini 3', 'Tesla Model Y', 'BMW i4', 'Mercedes EQS',
    'Audi e-tron GT', 'Porsche Taycan', 'Lucid Air', 'Rivian R1T', 'Ford F-150 Lightning'
];

const sampleCategories = [
    'Electronics', 'Smartphones', 'Laptops', 'Tablets', 'Audio', 'Gaming', 'Wearables',
    'Cameras', 'Drones', 'Automotive', 'Electric Vehicles', 'Accessories'
];

const sampleBrands = [
    'Apple', 'Samsung', 'Dell', 'Microsoft', 'Sony', 'Nintendo', 'Canon', 'DJI',
    'Tesla', 'BMW', 'Mercedes', 'Audi', 'Porsche', 'Lucid', 'Rivian', 'Ford'
];

// Generate sample data
function generateSampleData(count = 1000) {
    const data = [];
    const marketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
    const productTypes = ['T-Shirt', 'Sweatshirt', 'Hoodie', 'Tank Top', 'Long Sleeve'];
    const statuses = ['Active', 'Timed out', 'Pending', 'Rejected', 'Draft'];
    const brands = ['Gildan', 'Bella+Canvas', 'Next Level', 'American Apparel', 'Hanes'];

    const productTitles = [
        'Nature Scene Raglan - Pink',
        'Abstract Art Design - Blue',
        'Vintage Logo Tee - Black',
        'Minimalist Pattern - White',
        'Retro Style Shirt - Gray',
        'Modern Graphic Tee - Navy',
        'Classic Design - Red',
        'Artistic Print - Green',
        'Geometric Pattern - Purple',
        'Floral Design - Yellow'
    ];

    for (let i = 0; i < count; i++) {
        const marketplace = marketplaces[Math.floor(Math.random() * marketplaces.length)];
        const lastUpdated = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
        const firstPublished = new Date(lastUpdated.getTime() - Math.random() * 90 * 24 * 60 * 60 * 1000);
        const firstSold = Math.random() > 0.3 ? new Date(firstPublished.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000) : null;
        const lastSold = firstSold && Math.random() > 0.5 ? new Date(firstSold.getTime() + Math.random() * 60 * 24 * 60 * 60 * 1000) : firstSold;

        data.push({
            id: i + 1,
            marketplace: marketplace,
            asin: `${marketplace}${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
            productType: productTypes[Math.floor(Math.random() * productTypes.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            brand: brands[Math.floor(Math.random() * brands.length)],
            productTitle: productTitles[Math.floor(Math.random() * productTitles.length)],
            price: '$' + (Math.random() * 50 + 10).toFixed(2),
            designId: `DES${Math.floor(Math.random() * 900000) + 100000}`,
            lastUpdated: lastUpdated,
            reviews: Math.floor(Math.random() * 1000),
            sales: Math.floor(Math.random() * 50),
            returns: Math.floor(Math.random() * 5),
            returnRate: Math.random() > 0.7 ? (Math.random() * 15).toFixed(1) + '%' : '0%',
            royalties: '$' + (Math.random() * 20 + 5).toFixed(2),
            firstSold: firstSold,
            lastSold: lastSold,
            bsr: Math.floor(Math.random() * 1000000) + 1000,
            firstPublished: firstPublished
        });
    }

    return data;
}

// Column definitions
const columnDefs = [
    {
        field: 'id',
        headerName: 'ID',
        width: 80,
        type: 'number',
        sortable: true,
        filterable: true
    },
    {
        field: 'name',
        headerName: 'Product Name',
        width: 250,
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'category',
        headerName: 'Category',
        width: 150,
        sortable: true,
        filterable: true
    },
    {
        field: 'brand',
        headerName: 'Brand',
        width: 120,
        sortable: true,
        filterable: true
    },
    {
        field: 'price',
        headerName: 'Price',
        width: 120,
        type: 'currency',
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'stock',
        headerName: 'Stock',
        width: 100,
        type: 'number',
        sortable: true,
        filterable: true,
        editable: true
    },
    {
        field: 'rating',
        headerName: 'Rating',
        width: 100,
        type: 'number',
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            const stars = '★'.repeat(Math.floor(value)) + '☆'.repeat(5 - Math.floor(value));
            return `<span title="${value}/5">${stars}</span>`;
        }
    },
    {
        field: 'inStock',
        headerName: 'In Stock',
        width: 100,
        type: 'boolean',
        sortable: true,
        filterable: true,
        cellRenderer: (value) => {
            const color = value ? '#4CAF50' : '#F44336';
            const text = value ? 'Yes' : 'No';
            return `<span style="color: ${color}; font-weight: 500;">${text}</span>`;
        }
    },
    {
        field: 'releaseDate',
        headerName: 'Release Date',
        width: 140,
        type: 'date',
        sortable: true,
        filterable: true
    },
    {
        field: 'sales',
        headerName: 'Sales',
        width: 120,
        type: 'number',
        sortable: true,
        filterable: true
    },
    {
        field: 'revenue',
        headerName: 'Revenue',
        width: 140,
        type: 'currency',
        sortable: true,
        filterable: true
    }
];

// Initialize the demo
function initDemo() {
    console.log('🚀 Initializing SnapGrid Demo...');
    
    // Generate initial data
    currentData = generateSampleData(1000);
    
    // Create grid
    createGrid();
    
    // Set up event listeners
    setupEventListeners();
    
    // Update stats
    updateStats();
    
    console.log('✅ SnapGrid Demo initialized successfully');
}

// Create the grid instance
function createGrid() {
    const container = document.getElementById('demoGrid');
    
    if (grid) {
        grid.destroy();
    }
    
    grid = new SnapGrid(container, {
        data: currentData,
        columns: columnDefs,
        virtualScrolling: true,
        sortable: true,
        filterable: true,
        resizable: true,
        selectable: true,
        editable: true,
        theme: 'default',
        
        // Callbacks
        onRowClick: (rowData, rowIndex, event) => {
            console.log('Row clicked:', rowData);
            updateStats();
        },
        
        onCellEdit: (field, newValue, oldValue, rowData, rowIndex) => {
            console.log('Cell edited:', { field, newValue, oldValue, rowData });
            
            // Recalculate revenue if price or sales changed
            if (field === 'price' || field === 'sales') {
                rowData.revenue = rowData.price * rowData.sales;
                grid.refresh();
            }
            
            updateStats();
        },
        
        onSort: (field, direction) => {
            console.log('Sort applied:', { field, direction });
            updateStats();
        },
        
        onFilter: (field, value, type) => {
            console.log('Filter applied:', { field, value, type });
            updateStats();
        }
    });
}

// Set up event listeners
function setupEventListeners() {
    // Theme selector
    document.getElementById('themeSelect').addEventListener('change', (e) => {
        const theme = e.target.value;
        const container = document.getElementById('demoGrid');
        container.className = `snap-grid ${theme}`;
        grid.options.theme = theme;
    });
    
    // Row count selector
    document.getElementById('rowCountSelect').addEventListener('change', (e) => {
        const count = parseInt(e.target.value);
        console.log(`🔄 Generating ${count} rows...`);
        
        const startTime = performance.now();
        currentData = generateSampleData(count);
        grid.updateData(currentData);
        const endTime = performance.now();
        
        console.log(`✅ Generated ${count} rows in ${(endTime - startTime).toFixed(2)}ms`);
        updateStats();
    });
    
    // Filter input
    document.getElementById('filterInput').addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
            applyFilter();
        }
    });
}

// Apply filter
function applyFilter() {
    const filterValue = document.getElementById('filterInput').value;
    if (filterValue.trim()) {
        grid.setFilter('name', filterValue, 'contains');
    } else {
        clearFilter();
    }
    updateStats();
}

// Clear filter
function clearFilter() {
    document.getElementById('filterInput').value = '';
    grid.clearFilters();
    updateStats();
}

// Export data
function exportData() {
    const filename = `snap-grid-export-${new Date().toISOString().split('T')[0]}.csv`;
    grid.exportToCsv(filename);
    console.log('📊 Data exported to:', filename);
}

// Refresh grid
function refreshGrid() {
    grid.refresh();
    updateStats();
    console.log('🔄 Grid refreshed');
}

// Clear selection
function clearSelection() {
    grid.clearSelection();
    updateStats();
    console.log('❌ Selection cleared');
}

// Update statistics
function updateStats() {
    if (!grid) return;
    
    const stats = grid.getStats();
    
    document.getElementById('totalRows').textContent = stats.totalRows.toLocaleString();
    document.getElementById('filteredRows').textContent = stats.filteredRows.toLocaleString();
    document.getElementById('selectedRows').textContent = stats.selectedRows.toLocaleString();
    document.getElementById('renderTime').textContent = `${stats.lastRenderDuration.toFixed(1)}ms`;
}

// Toggle theme
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    // Update button text
    const button = document.querySelector('.theme-toggle');
    button.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
}

// Initialize theme from localStorage
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    const button = document.querySelector('.theme-toggle');
    button.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    initTheme();
    initDemo();
});

// Performance monitoring
setInterval(() => {
    if (grid) {
        updateStats();
    }
}, 1000);
